import { ItemsContainer } from "./items";
import { PaymentContainer } from "./payment";
import { ResumeCard } from "./resume";

export const TerminalGrid: React.FC = () => (
	<div className="flex flex-col lg:max-h-[calc(100vh-120px)] flex-1 lg:flex-row mb-20 sm:mb-0 gap-4 w-full max-w-full ">
		<div className="md:flex-[2] min-w-0">
			<ItemsContainer className="h-full max-h-full w-full" />
		</div>
		<div className="flex flex-col bg-black gap-4 md:flex-[1] min-h-0 min-w-0">
			<PaymentContainer className="flex-[3] min-h-0 w-full" />
			<ResumeCard />
		</div>
	</div>
);
